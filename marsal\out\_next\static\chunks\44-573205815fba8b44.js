"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[44],{2044:(e,r,t)=>{t.d(r,{A:()=>u,AuthProvider:()=>l});var s=t(5155),a=t(2115),o=t(5695),i=t(9323),n=t(3843);let c=(0,a.createContext)(void 0);function l(e){let{children:r}=e,[t,l]=(0,a.useState)(null),[u,d]=(0,a.useState)(!1),[m,g]=(0,a.useState)(!0),h=(0,o.useRouter)();(0,a.useEffect)(()=>{(async()=>{try{if(n.vQ.demo.enabled&&n.vQ.demo.autoLogin)try{console.log("Starting demo auto-login...");let e=await i.y.login({username:n.vQ.demo.defaultUser,password:"123456"});console.log("Demo user logged in:",e),l(e),d(!0),localStorage.setItem("user",JSON.stringify(e)),localStorage.setItem("isAuthenticated","true"),document.cookie="isAuthenticated=true; path=/; max-age=86400",g(!1);return}catch(e){console.error("Demo auto-login failed:",e)}{let e=localStorage.getItem("user")||localStorage.getItem("auth_user"),r=localStorage.getItem("isAuthenticated");if("true"===r&&e){let r=JSON.parse(e);l(r),d(!0),document.cookie="isAuthenticated=true; path=/; max-age=86400"}else{let e=i.y.getCurrentUser();e&&(l(e),d(!0),localStorage.setItem("user",JSON.stringify(e)),localStorage.setItem("isAuthenticated","true"),document.cookie="isAuthenticated=true; path=/; max-age=86400")}}}catch(e){console.error("Error checking authentication:",e),localStorage.removeItem("user"),localStorage.removeItem("auth_user"),localStorage.removeItem("isAuthenticated"),document.cookie="isAuthenticated=; path=/; max-age=0"}finally{setTimeout(()=>{g(!1)},300)}})()},[]);let _=async(e,r)=>{try{let t=await i.y.login({username:e,password:r});return l(t),d(!0),localStorage.setItem("user",JSON.stringify(t)),localStorage.setItem("isAuthenticated","true"),document.cookie="isAuthenticated=true; path=/; max-age=86400; SameSite=Lax",!0}catch(e){return console.error("Login error:",e),!1}};return m?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse",children:(0,s.jsx)("div",{className:"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"})}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"مرسال"}),(0,s.jsx)("p",{className:"text-gray-500",children:"جاري التحقق من بيانات الدخول..."})]})}):(0,s.jsx)(c.Provider,{value:{user:t,isAuthenticated:u,login:_,logout:()=>{try{i.y.logout(),l(null),d(!1),localStorage.removeItem("user"),localStorage.removeItem("isAuthenticated"),document.cookie="isAuthenticated=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT; SameSite=Lax",h.push("/login")}catch(e){console.error("Logout error:",e),h.push("/login")}},loading:m},children:r})}function u(){let e=(0,a.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},2099:(e,r,t)=>{t.d(r,{Dv:()=>n,LC:()=>a,ND:()=>s,Qo:()=>c});let s=(0,t(5647).UU)("https://ltxyomylyagbhueuyws.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.NeoJk-ReFqIy0QC8e-9lbg55tmu6snAmQOby0kgJDYo"),a=async()=>{try{let{data:e,error:r}=await s.from("users").select("id").limit(1);if(r){if(console.error("Supabase connection test failed:",r),r.message.includes('relation "users" does not exist'))return{success:!1,message:"⚠️ قاعدة البيانات غير مُعدة! يرجى تنفيذ ملف database-setup.sql في Supabase SQL Editor"};return{success:!1,message:"فشل في الاتصال بقاعدة البيانات السحابية: ".concat(r.message)}}let{data:t,error:a}=await s.from("users").select("username").eq("username","manager").single();if(a||!t)return{success:!1,message:"⚠️ قاعدة البيانات فارغة! يرجى تنفيذ ملف database-setup.sql لإضافة البيانات التجريبية"};return{success:!0,message:"تم الاتصال بقاعدة البيانات السحابية بنجاح ✅ (البيانات التجريبية متوفرة)"}}catch(r){console.error("Supabase connection test failed:",r);let e="فشل في الاتصال بقاعدة البيانات السحابية";return r instanceof Error&&(r.message.includes("fetch")?e+=" - تحقق من الاتصال بالإنترنت أو إعدادات Supabase":r.message.includes("permission")?e+=" - مشكلة في الصلاحيات":e+=": "+r.message),{success:!1,message:e}}};class o{async createUser(e){let{data:r,error:t}=await s.from("users").insert([e]).select().single();if(t)throw Error("Failed to create user: ".concat(t.message));return r}async getUserByUsername(e){let{data:r,error:t}=await s.from("users").select("*").eq("username",e).single();if(t&&"PGRST116"!==t.code)throw Error("Failed to get user: ".concat(t.message));return r||null}async getAllUsers(){let{data:e,error:r}=await s.from("users").select("*").order("created_at",{ascending:!1});if(r)throw Error("Failed to get users: ".concat(r.message));return e||[]}async updateUser(e,r){let{error:t}=await s.from("users").update(r).eq("id",e);if(t)throw Error("Failed to update user: ".concat(t.message))}async deleteUser(e){let{error:r}=await s.from("users").delete().eq("id",e);if(r)throw Error("Failed to delete user: ".concat(r.message))}async getUsersByRole(e){let{data:r,error:t}=await s.from("users").select("*").eq("role",e).order("created_at",{ascending:!1});if(t)throw Error("Failed to get users by role: ".concat(t.message));return r||[]}}class i{async createOrder(e){let{data:r,error:t}=await s.from("orders").insert([{...e,updated_at:new Date().toISOString()}]).select().single();if(t)throw Error("Failed to create order: ".concat(t.message));return r}async getOrderByTrackingNumber(e){let{data:r,error:t}=await s.from("orders").select("*").eq("tracking_number",e).single();if(t&&"PGRST116"!==t.code)throw Error("Failed to get order: ".concat(t.message));return r||null}async getAllOrders(){let{data:e,error:r}=await s.from("orders").select("*").order("created_at",{ascending:!1});if(r)throw Error("Failed to get orders: ".concat(r.message));return e||[]}async getOrdersByStatus(e){let{data:r,error:t}=await s.from("orders").select("*").eq("status",e).order("created_at",{ascending:!1});if(t)throw Error("Failed to get orders by status: ".concat(t.message));return r||[]}async getOrdersByCourier(e){let{data:r,error:t}=await s.from("orders").select("*").eq("courier_id",e).order("created_at",{ascending:!1});if(t)throw Error("Failed to get orders by courier: ".concat(t.message));return r||[]}async updateOrder(e,r){let{error:t}=await s.from("orders").update({...r,updated_at:new Date().toISOString()}).eq("id",e);if(t)throw Error("Failed to update order: ".concat(t.message))}async deleteOrder(e){let{error:r}=await s.from("orders").delete().eq("id",e);if(r)throw Error("Failed to delete order: ".concat(r.message))}async searchOrders(e){let{data:r,error:t}=await s.from("orders").select("*").or("tracking_number.ilike.%".concat(e,"%,customer_name.ilike.%").concat(e,"%,customer_phone.ilike.%").concat(e,"%,address.ilike.%").concat(e,"%")).order("created_at",{ascending:!1});if(t)throw Error("Failed to search orders: ".concat(t.message));return r||[]}}let n=new o,c=new i},3843:(e,r,t)=>{t.d(r,{vQ:()=>a});var s=t(9509);let a={name:"مرسال",description:"نظام إدارة عمليات التوصيل السريع",version:"1.1.0",colors:{primary:"#41a7ff",background:"#f0f3f5",accent:"#b19cd9"},business:{commissionPerOrder:1e3,currency:"د.ع",defaultOrderStatuses:["pending","assigned","picked_up","in_transit","delivered","returned","cancelled","postponed"]},api:{baseUrl:s.env.NEXT_PUBLIC_API_URL||"http://localhost:3001",endpoints:{orders:"/api/orders",users:"/api/users",couriers:"/api/couriers",settlements:"/api/settlements"}},firebase:{apiKey:s.env.NEXT_PUBLIC_FIREBASE_API_KEY,authDomain:s.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,projectId:s.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,storageBucket:s.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,messagingSenderId:s.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,appId:s.env.NEXT_PUBLIC_FIREBASE_APP_ID},features:{enableNotifications:!0,enableReports:!0,enableBulkOperations:!0,enableImageUpload:!0},demo:{enabled:!0,autoLogin:!0,defaultUser:"manager",skipFirebase:!0,showDemoNotice:!0},company:{name:"مكتب علي الشيباني للتوصيل السريع فرع الحي",phone:"+964 ************",address:"بغداد، العراق"},receipt:{dimensions:{width:"110mm",height:"130mm"},companyName:"مكتب علي الشيباني للتوصيل السريع فرع الحي",showBarcode:!0,showDate:!0,priceFormat:"en-US",currency:"IQD",fields:{trackingNumber:!0,customerPhone:!0,status:!0,courierName:!0,amount:!0,barcode:!0,date:!0}}}},6912:(e,r,t)=>{t.d(r,{JI:()=>i,_m:()=>o,gG:()=>s});var s=function(e){return e.MANAGER="manager",e.COURIER="courier",e.SUPERVISOR="supervisor",e}({});let a={manager:{role:"manager",permissions:["create_order","view_order","update_order","delete_order","assign_order","transfer_order","create_user","view_user","update_user","delete_user","manage_branches","manage_provinces","view_accounting","process_accounting","view_all_statistics","view_archive","manage_archive","manage_tickets","manage_settings","manage_warehouse","import_orders","export_orders"],canCreateRoles:["courier","supervisor"],accessibleSections:["orders","dispatch","returns","accounting","statistics","archive","users","import-export","notifications","settings"]},supervisor:{role:"supervisor",permissions:["view_order","update_order","assign_order","manage_tickets","view_statistics","view_archive"],canCreateRoles:[],accessibleSections:["orders","statistics","archive","notifications","settings"]},courier:{role:"courier",permissions:["view_order","update_order"],canCreateRoles:[],accessibleSections:["orders","archive","notifications","settings"]}};function o(e,r){var t;return(null==(t=a[e])?void 0:t.permissions.includes(r))||!1}function i(e){var r;return(null==(r=a[e])?void 0:r.accessibleSections)||[]}},9323:(e,r,t)=>{t.d(r,{y:()=>i}),t(2115);var s=t(6912),a=t(2099);class o{async login(e){try{let r=null;try{r=await a.Dv.getUserByUsername(e.username)}catch(r){return console.warn("Database not available, using fallback auth:",r),this.fallbackLogin(e)}if(!r)return this.fallbackLogin(e);if(!r.is_active)throw Error("الحساب غير مفعل");if("123456"!==e.password)throw Error("كلمة المرور غير صحيحة");let t={id:r.id,username:r.username,name:r.name,phone:r.phone,role:r.role,permissions:[],locationId:r.location_id||"main_center",location:r.location||{id:"main_center",name:"المركز العام",type:"company"},createdBy:r.created_by,createdAt:new Date(r.created_at),isActive:r.is_active,accessToken:"supabase_access_token",refreshToken:"supabase_refresh_token"};return this.currentUser=t,this.notifyListeners(),localStorage.setItem("auth_user",JSON.stringify(t)),t}catch(e){if(console.error("Login error:",e),e instanceof Error)throw e;throw Error("فشل في تسجيل الدخول")}}async fallbackLogin(e){let r={manager:{id:"manager_1",username:"manager",name:"مدير النظام",phone:"07901234567",role:"manager",permissions:[],locationId:"main_center",location:{id:"main_center",name:"المركز العام",type:"company"},createdBy:"system",createdAt:new Date,isActive:!0,accessToken:"fallback_access_token",refreshToken:"fallback_refresh_token"},supervisor:{id:"supervisor_1",username:"supervisor",name:"متابع النظام",phone:"07901234568",role:"supervisor",permissions:[],locationId:"main_center",location:{id:"main_center",name:"المركز العام",type:"company"},createdBy:"manager_1",createdAt:new Date,isActive:!0,accessToken:"fallback_access_token_supervisor",refreshToken:"fallback_refresh_token_supervisor"},courier:{id:"courier_1",username:"courier",name:"مندوب التوصيل",phone:"07901234570",role:"courier",permissions:[],locationId:"main_center",location:{id:"main_center",name:"المركز العام",type:"company"},createdBy:"manager_1",createdAt:new Date,isActive:!0,accessToken:"fallback_access_token_courier",refreshToken:"fallback_refresh_token_courier"}}[e.username];if(!r||"123456"!==e.password)throw Error("بيانات الدخول غير صحيحة");return this.currentUser=r,this.notifyListeners(),localStorage.setItem("auth_user",JSON.stringify(r)),r}async logout(){this.currentUser=null,localStorage.removeItem("auth_user"),this.notifyListeners()}getCurrentUser(){if(!this.currentUser){let e=localStorage.getItem("auth_user");if(e)try{this.currentUser=JSON.parse(e)}catch(e){localStorage.removeItem("auth_user")}}return this.currentUser}hasPermission(e){let r=this.getCurrentUser();return!!r&&(0,s._m)(r.role,e)}getAccessibleSections(){let e=this.getCurrentUser();return e?(0,s.JI)(e.role):[]}canCreateRole(e){var r;let t=this.getCurrentUser();return!!t&&((null==(r=({manager:["supervisor","courier"],supervisor:["courier"],courier:[]})[t.role])?void 0:r.includes(e))||!1)}addListener(e){this.listeners.push(e)}removeListener(e){this.listeners=this.listeners.filter(r=>r!==e)}notifyListeners(){this.listeners.forEach(e=>e(this.currentUser))}async updateProfile(e){if(!this.currentUser)throw Error("لم يتم تسجيل الدخول");return await new Promise(e=>setTimeout(e,500)),this.currentUser={...this.currentUser,...e},localStorage.setItem("auth_user",JSON.stringify(this.currentUser)),this.notifyListeners(),this.currentUser}async changePassword(e,r){if(!this.currentUser)throw Error("لم يتم تسجيل الدخول");if(await new Promise(e=>setTimeout(e,1e3)),"123456"!==e)throw Error("كلمة المرور الحالية غير صحيحة");console.log("تم تغيير كلمة المرور بنجاح")}async validateSession(){if(!this.getCurrentUser())return!1;try{return await new Promise(e=>setTimeout(e,200)),!0}catch(e){return await this.logout(),!1}}constructor(){this.currentUser=null,this.listeners=[]}}let i=new o}}]);