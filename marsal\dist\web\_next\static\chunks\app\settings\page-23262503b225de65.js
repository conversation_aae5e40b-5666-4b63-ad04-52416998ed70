(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[662],{285:(e,s,a)=>{"use strict";a.d(s,{$:()=>i});var t=a(5155);a(2115);var r=a(4624),n=a(2085),l=a(9434);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:s,variant:a,size:n,asChild:i=!1,...d}=e,o=i?r.DX:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,l.cn)(c({variant:a,size:n,className:s})),...d})}},381:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},646:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1007:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1788:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2099:(e,s,a)=>{"use strict";a.d(s,{Dv:()=>c,LC:()=>r,ND:()=>t,Qo:()=>i});let t=(0,a(5647).UU)("https://ltxyomylyagbhueuyws.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0eHlvbXlseWFnYmh1ZXV1eXdzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MDMyNzQsImV4cCI6MjA2NzI3OTI3NH0.NeoJk-ReFqIy0QC8e-9lbg55tmu6snAmQOby0kgJDYo"),r=async()=>{try{let{data:e,error:s}=await t.from("users").select("id").limit(1);if(s){if(console.error("Supabase connection test failed:",s),s.message.includes('relation "users" does not exist'))return{success:!1,message:"⚠️ قاعدة البيانات غير مُعدة! يرجى تنفيذ ملف database-setup.sql في Supabase SQL Editor"};return{success:!1,message:"فشل في الاتصال بقاعدة البيانات السحابية: ".concat(s.message)}}let{data:a,error:r}=await t.from("users").select("username").eq("username","manager").single();if(r||!a)return{success:!1,message:"⚠️ قاعدة البيانات فارغة! يرجى تنفيذ ملف database-setup.sql لإضافة البيانات التجريبية"};return{success:!0,message:"تم الاتصال بقاعدة البيانات السحابية بنجاح ✅ (البيانات التجريبية متوفرة)"}}catch(s){console.error("Supabase connection test failed:",s);let e="فشل في الاتصال بقاعدة البيانات السحابية";return s instanceof Error&&(s.message.includes("fetch")?e+=" - تحقق من الاتصال بالإنترنت أو إعدادات Supabase":s.message.includes("permission")?e+=" - مشكلة في الصلاحيات":e+=": "+s.message),{success:!1,message:e}}};class n{async createUser(e){let{data:s,error:a}=await t.from("users").insert([e]).select().single();if(a)throw Error("Failed to create user: ".concat(a.message));return s}async getUserByUsername(e){let{data:s,error:a}=await t.from("users").select("*").eq("username",e).single();if(a&&"PGRST116"!==a.code)throw Error("Failed to get user: ".concat(a.message));return s||null}async getAllUsers(){let{data:e,error:s}=await t.from("users").select("*").order("created_at",{ascending:!1});if(s)throw Error("Failed to get users: ".concat(s.message));return e||[]}async updateUser(e,s){let{error:a}=await t.from("users").update(s).eq("id",e);if(a)throw Error("Failed to update user: ".concat(a.message))}async deleteUser(e){let{error:s}=await t.from("users").delete().eq("id",e);if(s)throw Error("Failed to delete user: ".concat(s.message))}async getUsersByRole(e){let{data:s,error:a}=await t.from("users").select("*").eq("role",e).order("created_at",{ascending:!1});if(a)throw Error("Failed to get users by role: ".concat(a.message));return s||[]}}class l{async createOrder(e){let{data:s,error:a}=await t.from("orders").insert([{...e,updated_at:new Date().toISOString()}]).select().single();if(a)throw Error("Failed to create order: ".concat(a.message));return s}async getOrderByTrackingNumber(e){let{data:s,error:a}=await t.from("orders").select("*").eq("tracking_number",e).single();if(a&&"PGRST116"!==a.code)throw Error("Failed to get order: ".concat(a.message));return s||null}async getAllOrders(){let{data:e,error:s}=await t.from("orders").select("*").order("created_at",{ascending:!1});if(s)throw Error("Failed to get orders: ".concat(s.message));return e||[]}async getOrdersByStatus(e){let{data:s,error:a}=await t.from("orders").select("*").eq("status",e).order("created_at",{ascending:!1});if(a)throw Error("Failed to get orders by status: ".concat(a.message));return s||[]}async getOrdersByCourier(e){let{data:s,error:a}=await t.from("orders").select("*").eq("courier_id",e).order("created_at",{ascending:!1});if(a)throw Error("Failed to get orders by courier: ".concat(a.message));return s||[]}async updateOrder(e,s){let{error:a}=await t.from("orders").update({...s,updated_at:new Date().toISOString()}).eq("id",e);if(a)throw Error("Failed to update order: ".concat(a.message))}async deleteOrder(e){let{error:s}=await t.from("orders").delete().eq("id",e);if(s)throw Error("Failed to delete order: ".concat(s.message))}async searchOrders(e){let{data:s,error:a}=await t.from("orders").select("*").or("tracking_number.ilike.%".concat(e,"%,customer_name.ilike.%").concat(e,"%,customer_phone.ilike.%").concat(e,"%,address.ilike.%").concat(e,"%")).order("created_at",{ascending:!1});if(a)throw Error("Failed to search orders: ".concat(a.message));return s||[]}}let c=new n,i=new l},2446:(e,s,a)=>{Promise.resolve().then(a.bind(a,3236))},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>n});var t=a(5155);a(2115);var r=a(9434);function n(e){let{className:s,type:a,...n}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...n})}},2657:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3236:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>S});var t=a(5155),r=a(2115),n=a(6695),l=a(285),c=a(2523),i=a(7340),d=a(381),o=a(1007),m=a(3861),u=a(9946);let x=(0,u.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),h=(0,u.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),g=(0,u.A)("test-tube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]),p=(0,u.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var f=a(4229);let y=(0,u.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),j=(0,u.A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),b=(0,u.A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var v=a(8749),w=a(2657),N=a(646),k=a(1788),A=a(2099),C=a(6874),M=a.n(C);function S(){let[e,s]=(0,r.useState)("profile"),[a,u]=(0,r.useState)(!1),[C,S]=(0,r.useState)(!1),[_,P]=(0,r.useState)(!1),[O,z]=(0,r.useState)(null),[I,B]=(0,r.useState)({name:"أحمد محمد",email:"<EMAIL>",phone:"07901234567",role:"مدير"}),[E,Z]=(0,r.useState)({notifications:{email:!0,sms:!0,push:!1},theme:"light",language:"ar",commissionPerOrder:1e3,autoAssignment:!1}),[F,R]=(0,r.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[q,T]=(0,r.useState)("idle"),[U,D]=(0,r.useState)(null),[L,V]=(0,r.useState)(!1),$=async()=>{u(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("تم تحديث الملف الشخصي بنجاح")}catch(e){alert("حدث خطأ أثناء التحديث")}finally{u(!1)}},J=async()=>{if(F.newPassword!==F.confirmPassword)return void alert("كلمة المرور الجديدة غير متطابقة");if(F.newPassword.length<6)return void alert("كلمة المرور يجب أن تكون 6 أحرف على الأقل");u(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("تم تغيير كلمة المرور بنجاح"),R({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){alert("حدث خطأ أثناء تغيير كلمة المرور")}finally{u(!1)}},W=async()=>{u(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("تم حفظ الإعدادات بنجاح")}catch(e){alert("حدث خطأ أثناء حفظ الإعدادات")}finally{u(!1)}},Q=async()=>{P(!0),z(null);try{let e=await (0,A.LC)();z(e)}catch(e){z({success:!1,message:"حدث خطأ أثناء اختبار الاتصال"})}finally{P(!1)}},H=async()=>{T("testing");try{await new Promise(e=>setTimeout(e,2e3)),D({connection:"متصل",collections:["orders","users","couriers","settings"],totalOrders:1250,totalUsers:45,lastBackup:"2024-01-15 14:30:00",version:"Firebase v9.15.0"}),T("success")}catch(e){T("error")}};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 animated-bg p-6",dir:"rtl",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto space-y-8",children:[(0,t.jsx)("div",{className:"flex items-center gap-4 mb-6",children:(0,t.jsx)(M(),{href:"/",children:(0,t.jsxs)(l.$,{variant:"outline",size:"sm",className:"flex items-center gap-2 glass",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),"العودة للرئيسية"]})})}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-gray-500 to-gray-700 rounded-3xl shadow-2xl mb-4",children:(0,t.jsx)(d.A,{className:"h-8 w-8 text-white"})}),(0,t.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent mb-2",children:"الإعدادات"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-lg",children:"تخصيص التطبيق وإدارة الحساب"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-4",children:(0,t.jsxs)("nav",{className:"space-y-2",children:[(0,t.jsxs)("button",{onClick:()=>s("profile"),className:"w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ".concat("profile"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),"الملف الشخصي"]}),(0,t.jsxs)("button",{onClick:()=>s("notifications"),className:"w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ".concat("notifications"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,t.jsx)(m.A,{className:"h-4 w-4"}),"الإشعارات"]}),(0,t.jsxs)("button",{onClick:()=>s("appearance"),className:"w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ".concat("appearance"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,t.jsx)(x,{className:"h-4 w-4"}),"المظهر"]}),(0,t.jsxs)("button",{onClick:()=>s("system"),className:"w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ".concat("system"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,t.jsx)(h,{className:"h-4 w-4"}),"النظام"]}),(0,t.jsxs)("button",{onClick:()=>s("database"),className:"w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ".concat("database"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,t.jsx)(g,{className:"h-4 w-4"}),"قاعدة البيانات"]}),(0,t.jsxs)("button",{onClick:()=>s("security"),className:"w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ".concat("security"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,t.jsx)(p,{className:"h-4 w-4"}),"الأمان"]})]})})})}),(0,t.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:["profile"===e&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"h-5 w-5"}),"الملف الشخصي"]}),(0,t.jsx)(n.BT,{children:"تحديث معلوماتك الشخصية"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"الاسم الكامل"}),(0,t.jsx)(c.p,{value:I.name,onChange:e=>B(s=>({...s,name:e.target.value})),placeholder:"أدخل الاسم الكامل"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"رقم الهاتف"}),(0,t.jsx)(c.p,{value:I.phone,onChange:e=>B(s=>({...s,phone:e.target.value})),placeholder:"07xxxxxxxxx"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"البريد الإلكتروني"}),(0,t.jsx)(c.p,{type:"email",value:I.email,onChange:e=>B(s=>({...s,email:e.target.value})),placeholder:"<EMAIL>"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"الدور"}),(0,t.jsx)(c.p,{value:I.role,disabled:!0,className:"bg-muted"})]}),(0,t.jsxs)(l.$,{onClick:$,disabled:a,className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),a?"جاري الحفظ...":"حفظ التغييرات"]})]})]}),"notifications"===e&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"h-5 w-5"}),"إعدادات الإشعارات"]}),(0,t.jsx)(n.BT,{children:"تخصيص طريقة استلام الإشعارات"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"إشعارات البريد الإلكتروني"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"استلام الإشعارات عبر البريد الإلكتروني"})]}),(0,t.jsx)("input",{type:"checkbox",checked:E.notifications.email,onChange:e=>Z(s=>({...s,notifications:{...s.notifications,email:e.target.checked}})),className:"w-4 h-4"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"إشعارات SMS"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"استلام الإشعارات عبر الرسائل النصية"})]}),(0,t.jsx)("input",{type:"checkbox",checked:E.notifications.sms,onChange:e=>Z(s=>({...s,notifications:{...s.notifications,sms:e.target.checked}})),className:"w-4 h-4"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"الإشعارات الفورية"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"إشعارات فورية في المتصفح"})]}),(0,t.jsx)("input",{type:"checkbox",checked:E.notifications.push,onChange:e=>Z(s=>({...s,notifications:{...s.notifications,push:e.target.checked}})),className:"w-4 h-4"})]})]}),(0,t.jsxs)(l.$,{onClick:W,disabled:a,className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),a?"جاري الحفظ...":"حفظ الإعدادات"]})]})]}),"appearance"===e&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(x,{className:"h-5 w-5"}),"المظهر"]}),(0,t.jsx)(n.BT,{children:"تخصيص مظهر التطبيق"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"المظهر"}),(0,t.jsxs)("select",{value:E.theme,onChange:e=>Z(s=>({...s,theme:e.target.value})),className:"w-full p-3 border border-border rounded-md bg-background",children:[(0,t.jsx)("option",{value:"light",children:"فاتح"}),(0,t.jsx)("option",{value:"dark",children:"داكن"}),(0,t.jsx)("option",{value:"auto",children:"تلقائي"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اللغة"}),(0,t.jsxs)("select",{value:E.language,onChange:e=>Z(s=>({...s,language:e.target.value})),className:"w-full p-3 border border-border rounded-md bg-background",children:[(0,t.jsx)("option",{value:"ar",children:"العربية"}),(0,t.jsx)("option",{value:"en",children:"English"})]})]}),(0,t.jsxs)(l.$,{onClick:W,disabled:a,className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),a?"جاري الحفظ...":"حفظ الإعدادات"]})]})]}),"system"===e&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(h,{className:"h-5 w-5"}),"إعدادات النظام"]}),(0,t.jsx)(n.BT,{children:"إعدادات عامة للنظام"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"العمولة لكل طلب (دينار عراقي)"}),(0,t.jsx)(c.p,{type:"number",value:E.commissionPerOrder,onChange:e=>Z(s=>({...s,commissionPerOrder:parseInt(e.target.value)||0})),placeholder:"1000"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"الإسناد التلقائي"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"إسناد الطلبات تلقائياً للمندوبين"})]}),(0,t.jsx)("input",{type:"checkbox",checked:E.autoAssignment,onChange:e=>Z(s=>({...s,autoAssignment:e.target.checked})),className:"w-4 h-4"})]}),(0,t.jsxs)("div",{className:"border-t pt-6",children:[(0,t.jsx)("h3",{className:"font-medium mb-4",children:"اختبار الاتصال بقاعدة البيانات"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(l.$,{onClick:Q,disabled:_,variant:"outline",className:"flex items-center gap-2",children:[_?(0,t.jsx)(y,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(h,{className:"h-4 w-4"}),_?"جاري الاختبار...":"اختبار الاتصال"]}),O&&(0,t.jsxs)("div",{className:"p-4 rounded-lg border ".concat(O.success?"bg-green-50 border-green-200 text-green-800":"bg-red-50 border-red-200 text-red-800"),children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[O.success?(0,t.jsx)(j,{className:"h-4 w-4"}):(0,t.jsx)(b,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:O.success?"نجح الاتصال":"فشل الاتصال"})]}),(0,t.jsx)("p",{className:"mt-1 text-sm",children:O.message})]})]})]}),(0,t.jsxs)(l.$,{onClick:W,disabled:a,className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),a?"جاري الحفظ...":"حفظ الإعدادات"]})]})]}),"security"===e&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(p,{className:"h-5 w-5"}),"الأمان"]}),(0,t.jsx)(n.BT,{children:"تغيير كلمة المرور وإعدادات الأمان"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"كلمة المرور الحالية"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.p,{type:C?"text":"password",value:F.currentPassword,onChange:e=>R(s=>({...s,currentPassword:e.target.value})),placeholder:"أدخل كلمة المرور الحالية"}),(0,t.jsx)("button",{type:"button",onClick:()=>S(!C),className:"absolute left-3 top-1/2 transform -translate-y-1/2",children:C?(0,t.jsx)(v.A,{className:"h-4 w-4"}):(0,t.jsx)(w.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"كلمة المرور الجديدة"}),(0,t.jsx)(c.p,{type:"password",value:F.newPassword,onChange:e=>R(s=>({...s,newPassword:e.target.value})),placeholder:"أدخل كلمة المرور الجديدة"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"تأكيد كلمة المرور الجديدة"}),(0,t.jsx)(c.p,{type:"password",value:F.confirmPassword,onChange:e=>R(s=>({...s,confirmPassword:e.target.value})),placeholder:"أعد إدخال كلمة المرور الجديدة"})]}),(0,t.jsxs)(l.$,{onClick:J,disabled:a||!F.currentPassword||!F.newPassword||!F.confirmPassword,className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),a?"جاري التغيير...":"تغيير كلمة المرور"]})]})]}),"database"===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(g,{className:"h-5 w-5"}),"اختبار قاعدة البيانات"]}),(0,t.jsx)(n.BT,{children:"اختبار الاتصال بقاعدة البيانات والتحقق من حالتها"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"حالة الاتصال:"}),(0,t.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("success"===q?"bg-green-100 text-green-800":"error"===q?"bg-red-100 text-red-800":"testing"===q?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:"success"===q?"متصل":"error"===q?"خطأ":"testing"===q?"جاري الاختبار...":"غير مختبر"})]}),(0,t.jsx)(l.$,{onClick:H,disabled:"testing"===q,className:"w-full",children:"testing"===q?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(y,{className:"h-4 w-4 animate-spin"}),"جاري الاختبار..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g,{className:"h-4 w-4 ml-2"}),"اختبار قاعدة البيانات"]})}),U&&(0,t.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,t.jsx)(N.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsx)("span",{className:"font-medium text-green-800",children:"قاعدة البيانات متصلة"})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"الحالة:"}),(0,t.jsx)("span",{className:"font-medium",children:U.connection})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"إجمالي الطلبات:"}),(0,t.jsx)("span",{className:"font-medium",children:U.totalOrders})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"المستخدمين:"}),(0,t.jsx)("span",{className:"font-medium",children:U.totalUsers})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"آخر نسخة احتياطية:"}),(0,t.jsx)("span",{className:"font-medium",children:U.lastBackup})]})]})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(h,{className:"h-5 w-5"}),"إدارة قاعدة البيانات"]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)(l.$,{onClick:()=>V(!L),variant:"outline",className:"w-full",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 ml-2"}),L?"إخفاء":"عرض"," قاعدة البيانات"]}),(0,t.jsxs)(l.$,{onClick:()=>{let e=new Blob([JSON.stringify({orders:[],users:[],settings:{},exportDate:new Date().toISOString()},null,2)],{type:"application/json"}),s=URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download="marsal_backup_".concat(new Date().toISOString().split("T")[0],".json"),a.click(),URL.revokeObjectURL(s)},variant:"outline",className:"w-full",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 ml-2"}),"تصدير نسخة احتياطية"]})]})]}),L&&U&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(h,{className:"h-5 w-5"}),"بيانات قاعدة البيانات"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-auto",children:(0,t.jsx)("pre",{children:JSON.stringify(U,null,2)})})})]})]})]})]})]})})}},3861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},4229:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>i,Wu:()=>d,ZB:()=>c,Zp:()=>n,aR:()=>l});var t=a(5155);a(2115);var r=a(9434);function n(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a})}function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...a})}function i(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...a})}},7340:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},8749:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},9434:(e,s,a)=>{"use strict";a.d(s,{Yq:()=>c,cn:()=>n,ps:()=>u,qY:()=>m,r6:()=>i,vv:()=>l,y7:()=>d,zC:()=>o});var t=a(2596),r=a(9688);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}function l(e){return"".concat(e.toLocaleString("ar-IQ")," د.ع")}function c(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function i(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function d(){let e=Date.now().toString().slice(-6),s=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return"".concat("MRS").concat(e).concat(s)}function o(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function m(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function u(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}}},e=>{var s=s=>e(e.s=s);e.O(0,[497,874,647,441,684,358],()=>s(2446)),_N_E=e.O()}]);