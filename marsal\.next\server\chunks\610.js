exports.id=610,exports.ids=[610],exports.modules={5475:(e,r,s)=>{"use strict";s.d(r,{vQ:()=>t});let t={name:"مرسال",description:"نظام إدارة عمليات التوصيل السريع",version:"1.1.0",colors:{primary:"#41a7ff",background:"#f0f3f5",accent:"#b19cd9"},business:{commissionPerOrder:1e3,currency:"د.ع",defaultOrderStatuses:["pending","assigned","picked_up","in_transit","delivered","returned","cancelled","postponed"]},api:{baseUrl:process.env.NEXT_PUBLIC_API_URL||"http://localhost:3001",endpoints:{orders:"/api/orders",users:"/api/users",couriers:"/api/couriers",settlements:"/api/settlements"}},firebase:{apiKey:process.env.NEXT_PUBLIC_FIREBASE_API_KEY,authDomain:process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,projectId:process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,storageBucket:process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,messagingSenderId:process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,appId:process.env.NEXT_PUBLIC_FIREBASE_APP_ID},features:{enableNotifications:!0,enableReports:!0,enableBulkOperations:!0,enableImageUpload:!0},demo:{enabled:!0,autoLogin:!0,defaultUser:"manager",skipFirebase:!0,showDemoNotice:!0},company:{name:"مكتب علي الشيباني للتوصيل السريع فرع الحي",phone:"+964 ************",address:"بغداد، العراق"},receipt:{dimensions:{width:"110mm",height:"130mm"},companyName:"مكتب علي الشيباني للتوصيل السريع فرع الحي",showBarcode:!0,showDate:!0,priceFormat:"en-US",currency:"IQD",fields:{trackingNumber:!0,customerPhone:!0,status:!0,courierName:!0,amount:!0,barcode:!0,date:!0}}}},16391:(e,r,s)=>{"use strict";s.d(r,{Dv:()=>n,LC:()=>i,ND:()=>t,Qo:()=>c});let t=(0,s(60463).UU)("https://ltxyomylyagbhueuyws.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0eHlvbXlseWFnYmh1ZXV1eXdzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MDMyNzQsImV4cCI6MjA2NzI3OTI3NH0.NeoJk-ReFqIy0QC8e-9lbg55tmu6snAmQOby0kgJDYo"),i=async()=>{try{let{data:e,error:r}=await t.from("users").select("id").limit(1);if(r){if(console.error("Supabase connection test failed:",r),r.message.includes('relation "users" does not exist'))return{success:!1,message:"⚠️ قاعدة البيانات غير مُعدة! يرجى تنفيذ ملف database-setup.sql في Supabase SQL Editor"};return{success:!1,message:`فشل في الاتصال بقاعدة البيانات السحابية: ${r.message}`}}let{data:s,error:i}=await t.from("users").select("username").eq("username","manager").single();if(i||!s)return{success:!1,message:"⚠️ قاعدة البيانات فارغة! يرجى تنفيذ ملف database-setup.sql لإضافة البيانات التجريبية"};return{success:!0,message:"تم الاتصال بقاعدة البيانات السحابية بنجاح ✅ (البيانات التجريبية متوفرة)"}}catch(r){console.error("Supabase connection test failed:",r);let e="فشل في الاتصال بقاعدة البيانات السحابية";return r instanceof Error&&(r.message.includes("fetch")?e+=" - تحقق من الاتصال بالإنترنت أو إعدادات Supabase":r.message.includes("permission")?e+=" - مشكلة في الصلاحيات":e+=": "+r.message),{success:!1,message:e}}};class o{async createUser(e){let{data:r,error:s}=await t.from("users").insert([e]).select().single();if(s)throw Error(`Failed to create user: ${s.message}`);return r}async getUserByUsername(e){let{data:r,error:s}=await t.from("users").select("*").eq("username",e).single();if(s&&"PGRST116"!==s.code)throw Error(`Failed to get user: ${s.message}`);return r||null}async getAllUsers(){let{data:e,error:r}=await t.from("users").select("*").order("created_at",{ascending:!1});if(r)throw Error(`Failed to get users: ${r.message}`);return e||[]}async updateUser(e,r){let{error:s}=await t.from("users").update(r).eq("id",e);if(s)throw Error(`Failed to update user: ${s.message}`)}async deleteUser(e){let{error:r}=await t.from("users").delete().eq("id",e);if(r)throw Error(`Failed to delete user: ${r.message}`)}async getUsersByRole(e){let{data:r,error:s}=await t.from("users").select("*").eq("role",e).order("created_at",{ascending:!1});if(s)throw Error(`Failed to get users by role: ${s.message}`);return r||[]}}class a{async createOrder(e){let{data:r,error:s}=await t.from("orders").insert([{...e,updated_at:new Date().toISOString()}]).select().single();if(s)throw Error(`Failed to create order: ${s.message}`);return r}async getOrderByTrackingNumber(e){let{data:r,error:s}=await t.from("orders").select("*").eq("tracking_number",e).single();if(s&&"PGRST116"!==s.code)throw Error(`Failed to get order: ${s.message}`);return r||null}async getAllOrders(){let{data:e,error:r}=await t.from("orders").select("*").order("created_at",{ascending:!1});if(r)throw Error(`Failed to get orders: ${r.message}`);return e||[]}async getOrdersByStatus(e){let{data:r,error:s}=await t.from("orders").select("*").eq("status",e).order("created_at",{ascending:!1});if(s)throw Error(`Failed to get orders by status: ${s.message}`);return r||[]}async getOrdersByCourier(e){let{data:r,error:s}=await t.from("orders").select("*").eq("courier_id",e).order("created_at",{ascending:!1});if(s)throw Error(`Failed to get orders by courier: ${s.message}`);return r||[]}async updateOrder(e,r){let{error:s}=await t.from("orders").update({...r,updated_at:new Date().toISOString()}).eq("id",e);if(s)throw Error(`Failed to update order: ${s.message}`)}async deleteOrder(e){let{error:r}=await t.from("orders").delete().eq("id",e);if(r)throw Error(`Failed to delete order: ${r.message}`)}async searchOrders(e){let{data:r,error:s}=await t.from("orders").select("*").or(`tracking_number.ilike.%${e}%,customer_name.ilike.%${e}%,customer_phone.ilike.%${e}%,address.ilike.%${e}%`).order("created_at",{ascending:!1});if(s)throw Error(`Failed to search orders: ${s.message}`);return r||[]}}let n=new o,c=new a},19579:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},39727:()=>{},47990:()=>{},48126:(e,r,s)=>{"use strict";s.d(r,{JI:()=>a,_m:()=>o,gG:()=>t});var t=function(e){return e.MANAGER="manager",e.COURIER="courier",e.SUPERVISOR="supervisor",e}({});let i={manager:{role:"manager",permissions:["create_order","view_order","update_order","delete_order","assign_order","transfer_order","create_user","view_user","update_user","delete_user","manage_branches","manage_provinces","view_accounting","process_accounting","view_all_statistics","view_archive","manage_archive","manage_tickets","manage_settings","manage_warehouse","import_orders","export_orders"],canCreateRoles:["courier","supervisor"],accessibleSections:["orders","dispatch","returns","accounting","statistics","archive","users","import-export","notifications","settings"]},supervisor:{role:"supervisor",permissions:["view_order","update_order","assign_order","manage_tickets","view_statistics","view_archive"],canCreateRoles:[],accessibleSections:["orders","statistics","archive","notifications","settings"]},courier:{role:"courier",permissions:["view_order","update_order"],canCreateRoles:[],accessibleSections:["orders","archive","notifications","settings"]}};function o(e,r){return i[e]?.permissions.includes(r)||!1}function a(e){return i[e]?.accessibleSections||[]}},51937:(e,r,s)=>{Promise.resolve().then(s.bind(s,86088))},53923:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},61135:()=>{},63523:(e,r,s)=>{"use strict";s.d(r,{y:()=>a}),s(43210);var t=s(48126),i=s(16391);class o{async login(e){try{let r=null;try{r=await i.Dv.getUserByUsername(e.username)}catch(r){return console.warn("Database not available, using fallback auth:",r),this.fallbackLogin(e)}if(!r)return this.fallbackLogin(e);if(!r.is_active)throw Error("الحساب غير مفعل");if("123456"!==e.password)throw Error("كلمة المرور غير صحيحة");let s={id:r.id,username:r.username,name:r.name,phone:r.phone,role:r.role,permissions:[],locationId:r.location_id||"main_center",location:r.location||{id:"main_center",name:"المركز العام",type:"company"},createdBy:r.created_by,createdAt:new Date(r.created_at),isActive:r.is_active,accessToken:"supabase_access_token",refreshToken:"supabase_refresh_token"};return this.currentUser=s,this.notifyListeners(),s}catch(e){if(console.error("Login error:",e),e instanceof Error)throw e;throw Error("فشل في تسجيل الدخول")}}async fallbackLogin(e){let r={manager:{id:"manager_1",username:"manager",name:"مدير النظام",phone:"07901234567",role:"manager",permissions:[],locationId:"main_center",location:{id:"main_center",name:"المركز العام",type:"company"},createdBy:"system",createdAt:new Date,isActive:!0,accessToken:"fallback_access_token",refreshToken:"fallback_refresh_token"},supervisor:{id:"supervisor_1",username:"supervisor",name:"متابع النظام",phone:"07901234568",role:"supervisor",permissions:[],locationId:"main_center",location:{id:"main_center",name:"المركز العام",type:"company"},createdBy:"manager_1",createdAt:new Date,isActive:!0,accessToken:"fallback_access_token_supervisor",refreshToken:"fallback_refresh_token_supervisor"},courier:{id:"courier_1",username:"courier",name:"مندوب التوصيل",phone:"07901234570",role:"courier",permissions:[],locationId:"main_center",location:{id:"main_center",name:"المركز العام",type:"company"},createdBy:"manager_1",createdAt:new Date,isActive:!0,accessToken:"fallback_access_token_courier",refreshToken:"fallback_refresh_token_courier"}}[e.username];if(!r||"123456"!==e.password)throw Error("بيانات الدخول غير صحيحة");return this.currentUser=r,this.notifyListeners(),r}async logout(){this.currentUser=null,this.notifyListeners()}getCurrentUser(){return this.currentUser,this.currentUser}hasPermission(e){let r=this.getCurrentUser();return!!r&&(0,t._m)(r.role,e)}getAccessibleSections(){let e=this.getCurrentUser();return e?(0,t.JI)(e.role):[]}canCreateRole(e){let r=this.getCurrentUser();return!!r&&(({manager:["supervisor","courier"],supervisor:["courier"],courier:[]})[r.role]?.includes(e)||!1)}addListener(e){this.listeners.push(e)}removeListener(e){this.listeners=this.listeners.filter(r=>r!==e)}notifyListeners(){this.listeners.forEach(e=>e(this.currentUser))}async updateProfile(e){if(!this.currentUser)throw Error("لم يتم تسجيل الدخول");return await new Promise(e=>setTimeout(e,500)),this.currentUser={...this.currentUser,...e},this.notifyListeners(),this.currentUser}async changePassword(e,r){if(!this.currentUser)throw Error("لم يتم تسجيل الدخول");if(await new Promise(e=>setTimeout(e,1e3)),"123456"!==e)throw Error("كلمة المرور الحالية غير صحيحة");console.log("تم تغيير كلمة المرور بنجاح")}async validateSession(){if(!this.getCurrentUser())return!1;try{return await new Promise(e=>setTimeout(e,200)),!0}catch(e){return await this.logout(),!1}}constructor(){this.currentUser=null,this.listeners=[]}}let a=new o},67958:(e,r,s)=>{"use strict";s.d(r,{A:()=>l,AuthProvider:()=>c});var t=s(60687),i=s(43210),o=s(16189),a=s(63523);s(5475);let n=(0,i.createContext)(void 0);function c({children:e}){let[r,s]=(0,i.useState)(null),[c,l]=(0,i.useState)(!1),[d,u]=(0,i.useState)(!0),m=(0,o.useRouter)(),h=async(e,r)=>{try{let t=await a.y.login({username:e,password:r});return s(t),l(!0),!0}catch(e){return console.error("Login error:",e),!1}};return d?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse",children:(0,t.jsx)("div",{className:"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"مرسال"}),(0,t.jsx)("p",{className:"text-gray-500",children:"جاري التحقق من بيانات الدخول..."})]})}):(0,t.jsx)(n.Provider,{value:{user:r,isAuthenticated:c,login:h,logout:()=>{try{a.y.logout(),s(null),l(!1),m.push("/login")}catch(e){console.error("Logout error:",e),m.push("/login")}},loading:d},children:e})}function l(){let e=(0,i.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},86088:(e,r,s)=>{"use strict";s.d(r,{AuthProvider:()=>i});var t=s(12907);let i=(0,t.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\components\\auth-provider.tsx","AuthProvider");(0,t.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\components\\auth-provider.tsx","useAuth")},88385:(e,r,s)=>{Promise.resolve().then(s.bind(s,67958))},94431:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l,generateViewport:()=>c,metadata:()=>n});var t=s(37413),i=s(25091),o=s.n(i);s(61135);var a=s(86088);let n={title:"مرسال - نظام إدارة التوصيل",description:"نظام إدارة عمليات شركات التوصيل السريع",manifest:"/manifest.json"};function c(){return{width:"device-width",initialScale:1,maximumScale:5,userScalable:!0,themeColor:"#3B82F6",colorScheme:"light dark",viewportFit:"cover"}}function l({children:e}){return(0,t.jsx)("html",{lang:"ar",dir:"rtl",className:"h-full",children:(0,t.jsx)("body",{className:`${o().variable} antialiased h-full w-full overflow-x-hidden`,children:(0,t.jsx)(a.AuthProvider,{children:(0,t.jsx)("div",{className:"min-h-screen w-full max-w-full overflow-x-hidden",children:e})})})})}}};