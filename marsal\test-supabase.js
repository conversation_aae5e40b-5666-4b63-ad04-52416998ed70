// اختبار سريع لاتصال Supabase
const { createClient } = require('@supabase/supabase-js');

// إعدادات Supabase
const supabaseUrl = 'https://ltxyomylyagbhueuyws.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0eHlvbXlseWFnYmh1ZXV1eXdzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MDMyNzQsImV4cCI6MjA2NzI3OTI3NH0.NeoJk-ReFqIy0QC8e-9lbg55tmu6snAmQOby0kgJDYo';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  try {
    console.log('🔄 اختبار الاتصال بـ Supabase...');
    
    // اختبار الاتصال بجدول المستخدمين
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(1);
    
    if (usersError) {
      console.error('❌ خطأ في جدول المستخدمين:', usersError.message);
      return false;
    }
    
    console.log('✅ جدول المستخدمين متاح');
    console.log('👥 عدد المستخدمين:', users?.length || 0);
    
    // اختبار الاتصال بجدول الطلبات
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select('*')
      .limit(1);
    
    if (ordersError) {
      console.error('❌ خطأ في جدول الطلبات:', ordersError.message);
      return false;
    }
    
    console.log('✅ جدول الطلبات متاح');
    console.log('📦 عدد الطلبات:', orders?.length || 0);
    
    // اختبار تسجيل الدخول
    const { data: loginUser, error: loginError } = await supabase
      .from('users')
      .select('*')
      .eq('username', 'manager')
      .single();
    
    if (loginError) {
      console.error('❌ خطأ في البحث عن المدير:', loginError.message);
      return false;
    }
    
    if (loginUser) {
      console.log('✅ المستخدم المدير موجود:', loginUser.name);
    } else {
      console.log('⚠️ المستخدم المدير غير موجود');
    }
    
    console.log('🎉 جميع الاختبارات نجحت!');
    return true;
    
  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
    return false;
  }
}

// تشغيل الاختبار
testConnection().then(success => {
  if (success) {
    console.log('\n✅ قاعدة البيانات جاهزة للاستخدام!');
  } else {
    console.log('\n❌ هناك مشكلة في قاعدة البيانات');
  }
  process.exit(success ? 0 : 1);
});
