// نظام المصادقة والصلاحيات

import { useState, useEffect } from 'react';
import { UserRole, Permission, hasPermission, getAccessibleSections } from '@/types/roles';
import { User } from '@/types/roles';
import { supabase, userService, type User as SupabaseUser } from './supabase';

export interface AuthUser extends User {
  accessToken: string;
  refreshToken: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  password: string;
  phone: string;
  email?: string;
  role: UserRole;
  locationId: string;
  permissions: Permission[];
}

class AuthService {
  private currentUser: AuthUser | null = null;
  private listeners: ((user: AuthUser | null) => void)[] = [];

  // تسجيل الدخول
  async login(credentials: LoginCredentials): Promise<AuthUser> {
    try {
      // محاولة البحث عن المستخدم في قاعدة البيانات
      let user = null;
      try {
        user = await userService.getUserByUsername(credentials.username);
      } catch (dbError) {
        console.warn('Database not available, using fallback auth:', dbError);
        // في حالة عدم توفر قاعدة البيانات، استخدم المستخدمين التجريبيين
        return this.fallbackLogin(credentials);
      }

      if (!user) {
        // إذا لم يوجد المستخدم في قاعدة البيانات، جرب المستخدمين التجريبيين
        return this.fallbackLogin(credentials);
      }

      if (!user.is_active) {
        throw new Error('الحساب غير مفعل');
      }

      // في الوضع التجريبي، نقبل كلمة المرور 123456 لجميع المستخدمين
      if (credentials.password !== '123456') {
        throw new Error('كلمة المرور غير صحيحة');
      }

      // تحويل بيانات المستخدم من Supabase إلى AuthUser
      const authUser: AuthUser = {
        id: user.id,
        username: user.username,
        name: user.name,
        phone: user.phone,
        role: user.role as any,
        permissions: [],
        locationId: user.location_id || 'main_center',
        location: user.location || {
          id: 'main_center',
          name: 'المركز العام',
          type: 'company'
        },
        createdBy: user.created_by,
        createdAt: new Date(user.created_at),
        isActive: user.is_active,
        accessToken: 'supabase_access_token',
        refreshToken: 'supabase_refresh_token'
      };

      this.currentUser = authUser;
      this.notifyListeners();

      // حفظ في localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('auth_user', JSON.stringify(authUser));
      }

      return authUser;
    } catch (error) {
      console.error('Login error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('فشل في تسجيل الدخول');
    }
  }

  // تسجيل الدخول الاحتياطي (في حالة عدم توفر قاعدة البيانات)
  private async fallbackLogin(credentials: LoginCredentials): Promise<AuthUser> {
    // بيانات تجريبية للمستخدمين
    const mockUsers: Record<string, AuthUser> = {
      'manager': {
        id: 'manager_1',
        username: 'manager',
        name: 'مدير النظام',
        phone: '07901234567',
        role: 'manager' as any,
        permissions: [],
        locationId: 'main_center',
        location: {
          id: 'main_center',
          name: 'المركز العام',
          type: 'company'
        },
        createdBy: 'system',
        createdAt: new Date(),
        isActive: true,
        accessToken: 'fallback_access_token',
        refreshToken: 'fallback_refresh_token'
      },
      'supervisor': {
        id: 'supervisor_1',
        username: 'supervisor',
        name: 'متابع النظام',
        phone: '07901234568',
        role: 'supervisor' as any,
        permissions: [],
        locationId: 'main_center',
        location: {
          id: 'main_center',
          name: 'المركز العام',
          type: 'company'
        },
        createdBy: 'manager_1',
        createdAt: new Date(),
        isActive: true,
        accessToken: 'fallback_access_token_supervisor',
        refreshToken: 'fallback_refresh_token_supervisor'
      },
      'courier': {
        id: 'courier_1',
        username: 'courier',
        name: 'مندوب التوصيل',
        phone: '07901234570',
        role: 'courier' as any,
        permissions: [],
        locationId: 'main_center',
        location: {
          id: 'main_center',
          name: 'المركز العام',
          type: 'company'
        },
        createdBy: 'manager_1',
        createdAt: new Date(),
        isActive: true,
        accessToken: 'fallback_access_token_courier',
        refreshToken: 'fallback_refresh_token_courier'
      }
    };

    const user = mockUsers[credentials.username];
    if (!user || credentials.password !== '123456') {
      throw new Error('بيانات الدخول غير صحيحة');
    }

    this.currentUser = user;
    this.notifyListeners();

    // حفظ في localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_user', JSON.stringify(user));
    }

    return user;
  }

  // تسجيل الخروج
  async logout(): Promise<void> {
    this.currentUser = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_user');
    }
    this.notifyListeners();
  }

  // الحصول على المستخدم الحالي
  getCurrentUser(): AuthUser | null {
    if (!this.currentUser && typeof window !== 'undefined') {
      const stored = localStorage.getItem('auth_user');
      if (stored) {
        try {
          this.currentUser = JSON.parse(stored);
        } catch (error) {
          localStorage.removeItem('auth_user');
        }
      }
    }
    return this.currentUser;
  }

  // التحقق من الصلاحية
  hasPermission(permission: Permission): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;
    return hasPermission(user.role, permission);
  }

  // الحصول على الأقسام المتاحة
  getAccessibleSections(): string[] {
    const user = this.getCurrentUser();
    if (!user) return [];
    return getAccessibleSections(user.role);
  }

  // التحقق من إمكانية إنشاء دور معين
  canCreateRole(targetRole: string): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    const rolePermissions: Record<string, string[]> = {
      'manager': ['supervisor', 'courier'],
      'supervisor': ['courier'],
      'courier': []
    };

    return rolePermissions[user.role]?.includes(targetRole) || false;
  }

  // إضافة مستمع للتغييرات
  addListener(listener: (user: AuthUser | null) => void): void {
    this.listeners.push(listener);
  }

  // إزالة مستمع
  removeListener(listener: (user: AuthUser | null) => void): void {
    this.listeners = this.listeners.filter(l => l !== listener);
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.currentUser));
  }

  // تحديث بيانات المستخدم
  async updateProfile(data: Partial<User>): Promise<AuthUser> {
    if (!this.currentUser) {
      throw new Error('لم يتم تسجيل الدخول');
    }

    // محاكاة API call
    await new Promise(resolve => setTimeout(resolve, 500));

    this.currentUser = { ...this.currentUser, ...data };
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_user', JSON.stringify(this.currentUser));
    }
    this.notifyListeners();

    return this.currentUser;
  }

  // تغيير كلمة المرور
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    if (!this.currentUser) {
      throw new Error('لم يتم تسجيل الدخول');
    }

    // محاكاة API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    // في التطبيق الحقيقي، سيتم التحقق من كلمة المرور الحالية
    if (currentPassword !== '123456') {
      throw new Error('كلمة المرور الحالية غير صحيحة');
    }

    // تحديث كلمة المرور (في التطبيق الحقيقي)
    console.log('تم تغيير كلمة المرور بنجاح');
  }

  // التحقق من صحة الجلسة
  async validateSession(): Promise<boolean> {
    const user = this.getCurrentUser();
    if (!user) return false;

    try {
      // محاكاة التحقق من صحة الجلسة
      await new Promise(resolve => setTimeout(resolve, 200));
      return true;
    } catch (error) {
      await this.logout();
      return false;
    }
  }
}

export const authService = new AuthService();

// Hook للاستخدام في React
export function useAuth() {
  const [user, setUser] = useState<AuthUser | null>(authService.getCurrentUser());

  useEffect(() => {
    const listener = (newUser: AuthUser | null) => setUser(newUser);
    authService.addListener(listener);
    return () => authService.removeListener(listener);
  }, []);

  return {
    user,
    login: authService.login.bind(authService),
    logout: authService.logout.bind(authService),
    hasPermission: authService.hasPermission.bind(authService),
    getAccessibleSections: authService.getAccessibleSections.bind(authService),
    canCreateRole: authService.canCreateRole.bind(authService),
    updateProfile: authService.updateProfile.bind(authService),
    changePassword: authService.changePassword.bind(authService),
    validateSession: authService.validateSession.bind(authService)
  };
}


